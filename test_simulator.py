#!/usr/bin/env python3
"""
按键模拟器测试脚本
用于验证按键模拟功能是否正常工作
"""

import time
import threading
from pynput.keyboard import Controller, Listener
from pynput import keyboard

class KeySimulatorTest:
    def __init__(self):
        self.controller = Controller()
        self.test_results = []
        self.listener = None
        self.captured_keys = []
        
    def start_key_capture(self):
        """开始捕获按键"""
        def on_press(key):
            try:
                if hasattr(key, 'char') and key.char:
                    self.captured_keys.append(key.char)
                    print(f"捕获到按键: {key.char}")
                elif key == keyboard.Key.space:
                    self.captured_keys.append('space')
                    print("捕获到按键: space")
                elif key == keyboard.Key.enter:
                    self.captured_keys.append('enter')
                    print("捕获到按键: enter")
            except AttributeError:
                pass
        
        self.listener = Listener(on_press=on_press)
        self.listener.start()
        print("开始监听按键...")
    
    def stop_key_capture(self):
        """停止捕获按键"""
        if self.listener:
            self.listener.stop()
            print("停止监听按键")
    
    def test_single_key(self, key_name, duration=0.1):
        """测试单个按键模拟"""
        print(f"\n测试按键: {key_name}")
        try:
            if key_name == 'space':
                self.controller.press(keyboard.Key.space)
                time.sleep(duration)
                self.controller.release(keyboard.Key.space)
            elif key_name == 'enter':
                self.controller.press(keyboard.Key.enter)
                time.sleep(duration)
                self.controller.release(keyboard.Key.enter)
            else:
                self.controller.press(key_name)
                time.sleep(duration)
                self.controller.release(key_name)
            
            print(f"✓ 按键 {key_name} 模拟成功")
            return True
        except Exception as e:
            print(f"✗ 按键 {key_name} 模拟失败: {e}")
            return False
    
    def test_key_sequence(self, sequence, interval=0.5):
        """测试按键序列"""
        print(f"\n测试按键序列: {sequence}")
        success_count = 0
        
        for key in sequence:
            if self.test_single_key(key):
                success_count += 1
            time.sleep(interval)
        
        print(f"序列测试完成: {success_count}/{len(sequence)} 成功")
        return success_count == len(sequence)
    
    def test_timing_accuracy(self, key='a', target_interval=1.0, test_count=5):
        """测试时间间隔准确性"""
        print(f"\n测试时间间隔准确性 (目标间隔: {target_interval}秒)")
        intervals = []
        
        start_time = time.time()
        for i in range(test_count):
            self.test_single_key(key)
            if i > 0:
                current_time = time.time()
                interval = current_time - last_time
                intervals.append(interval)
                print(f"间隔 {i}: {interval:.3f}秒")
            last_time = time.time()
            time.sleep(target_interval)
        
        if intervals:
            avg_interval = sum(intervals) / len(intervals)
            print(f"平均间隔: {avg_interval:.3f}秒")
            accuracy = abs(avg_interval - target_interval) / target_interval * 100
            print(f"准确度: {100-accuracy:.1f}%")
            return accuracy < 10  # 允许10%的误差
        return False
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("=" * 50)
        print("按键模拟器综合测试")
        print("=" * 50)
        
        # 等待用户准备
        print("\n请确保:")
        print("1. 打开一个文本编辑器（如记事本）")
        print("2. 将光标放在编辑区域")
        print("3. 准备观察按键效果")
        input("\n按回车键开始测试...")
        
        # 开始捕获按键
        self.start_key_capture()
        time.sleep(1)
        
        test_results = []
        
        # 测试1: 基本字母按键
        print("\n" + "="*30)
        print("测试1: 基本字母按键")
        print("="*30)
        basic_keys = ['a', 'b', 'c']
        result1 = self.test_key_sequence(basic_keys, 0.5)
        test_results.append(("基本字母按键", result1))
        
        # 测试2: 特殊按键
        print("\n" + "="*30)
        print("测试2: 特殊按键")
        print("="*30)
        special_keys = ['space', 'enter']
        result2 = self.test_key_sequence(special_keys, 1.0)
        test_results.append(("特殊按键", result2))
        
        # 测试3: 混合序列
        print("\n" + "="*30)
        print("测试3: 混合按键序列")
        print("="*30)
        mixed_sequence = ['h', 'e', 'l', 'l', 'o', 'space', 'w', 'o', 'r', 'l', 'd']
        result3 = self.test_key_sequence(mixed_sequence, 0.3)
        test_results.append(("混合序列", result3))
        
        # 测试4: 时间间隔准确性
        print("\n" + "="*30)
        print("测试4: 时间间隔准确性")
        print("="*30)
        result4 = self.test_timing_accuracy('x', 0.5, 3)
        test_results.append(("时间间隔", result4))
        
        # 停止捕获
        time.sleep(2)
        self.stop_key_capture()
        
        # 输出测试结果
        print("\n" + "="*50)
        print("测试结果汇总")
        print("="*50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总体结果: {passed}/{total} 测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！按键模拟器工作正常。")
        else:
            print("⚠️  部分测试失败，请检查配置或权限。")
        
        print(f"\n捕获到的按键数量: {len(self.captured_keys)}")
        if self.captured_keys:
            print(f"最近捕获的按键: {self.captured_keys[-10:]}")

def main():
    """主函数"""
    tester = KeySimulatorTest()
    
    try:
        tester.run_comprehensive_test()
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
    finally:
        tester.stop_key_capture()
        print("\n测试完成")

if __name__ == "__main__":
    main()
