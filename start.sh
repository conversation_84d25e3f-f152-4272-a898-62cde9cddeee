#!/bin/bash

echo "========================================"
echo "       按键模拟器 - Key Simulator"
echo "========================================"
echo

echo "正在启动按键模拟器..."
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python 3.6+"
        echo "Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 检查pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "错误: 未找到pip，请先安装pip"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

# 检查依赖是否安装
echo "检查依赖包..."
if ! $PIP_CMD show pynput &> /dev/null; then
    echo "正在安装依赖包..."
    $PIP_CMD install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

# 启动应用
echo "启动应用程序..."
$PYTHON_CMD key_simulator.py

if [ $? -ne 0 ]; then
    echo
    echo "应用程序异常退出"
    read -p "按回车键退出..."
fi
