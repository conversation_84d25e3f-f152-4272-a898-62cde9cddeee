import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random
from pynput import keyboard, mouse
from pynput.keyboard import Key, Listener
from pynput.mouse import Button
import json
import os
import logging
from datetime import datetime

class KeySimulatorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("按键模拟器 - Key Simulator v2.0")
        self.root.geometry("600x700")
        self.root.resizable(True, True)

        # 设置日志
        self.setup_logging()
        
        # 应用状态
        self.is_running = False
        self.simulation_thread = None
        self.hotkey_listener = None
        self.start_time = None
        self.cycle_count = 0
        self.error_count = 0
        self.last_error_time = None
        
        # 配置参数
        self.config = {
            "target_key": "space",
            "min_interval": 0.5,
            "max_interval": 2.0,
            "press_duration": 0.1,
            "hotkey": "`",  # ~ 键
            "sequence_mode": False,
            "key_sequence": [],
            "sequence_repeat": True,
            "mouse_mode": False,
            "mouse_button": "left",
            "mouse_x": 100,
            "mouse_y": 100,
            "mouse_random_offset": 5,
            "click_count": 1,
            "max_duration": 0,  # 最大运行时长（秒），0表示无限制
            "max_cycles": 0,    # 最大循环次数，0表示无限制
            "delay_start": 0    # 延迟启动时间（秒）
        }
        
        self.load_config()
        self.setup_ui()
        self.start_hotkey_listener()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 记录启动日志
        logging.info("按键模拟器启动成功")

    def setup_logging(self):
        """设置日志记录"""
        try:
            # 创建logs目录
            if not os.path.exists("logs"):
                os.makedirs("logs")

            # 配置日志格式
            log_filename = f"logs/key_simulator_{datetime.now().strftime('%Y%m%d')}.log"
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(levelname)s - %(message)s',
                handlers=[
                    logging.FileHandler(log_filename, encoding='utf-8'),
                    logging.StreamHandler()
                ]
            )
        except Exception as e:
            print(f"日志设置失败: {e}")
    
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="按键模拟器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="已停止", foreground="red")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.hotkey_label = ttk.Label(status_frame, text=f"热键: {self.config['hotkey']} (开关)")
        self.hotkey_label.grid(row=1, column=0, sticky=tk.W)
        
        # 设置框架
        settings_frame = ttk.LabelFrame(main_frame, text="设置", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 模式选择
        mode_frame = ttk.Frame(settings_frame)
        mode_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.mode_var = tk.StringVar(value="single")
        ttk.Radiobutton(mode_frame, text="单键模式", variable=self.mode_var, value="single", command=self.on_mode_change).grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="序列模式", variable=self.mode_var, value="sequence", command=self.on_mode_change).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))
        ttk.Radiobutton(mode_frame, text="鼠标模式", variable=self.mode_var, value="mouse", command=self.on_mode_change).grid(row=0, column=2, sticky=tk.W, padx=(20, 0))

        # 单键设置框架
        self.single_frame = ttk.Frame(settings_frame)
        self.single_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(self.single_frame, text="目标按键:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.key_var = tk.StringVar(value=self.config["target_key"])
        key_combo = ttk.Combobox(self.single_frame, textvariable=self.key_var, width=20)

        # 扩展按键列表，包含组合键
        key_values = [
            # 基本按键
            'space', 'enter', 'tab', 'esc', 'backspace', 'delete',
            # 字母
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
            'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            # 数字
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            # 功能键
            'f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 'f11', 'f12',
            # 方向键
            'up', 'down', 'left', 'right',
            # 修饰键
            'shift', 'ctrl', 'alt', 'win',
            # 常用组合键
            'ctrl+c', 'ctrl+v', 'ctrl+x', 'ctrl+z', 'ctrl+y', 'ctrl+a', 'ctrl+s',
            'ctrl+n', 'ctrl+o', 'ctrl+p', 'ctrl+f', 'ctrl+h', 'ctrl+r',
            'alt+tab', 'alt+f4', 'shift+tab', 'ctrl+shift+esc',
            'win+d', 'win+l', 'win+r', 'win+e'
        ]

        key_combo['values'] = key_values
        key_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 序列设置框架
        self.sequence_frame = ttk.Frame(settings_frame)
        self.sequence_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(self.sequence_frame, text="按键序列:").grid(row=0, column=0, sticky=tk.W, pady=2)

        sequence_control_frame = ttk.Frame(self.sequence_frame)
        sequence_control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)

        self.sequence_entry = ttk.Entry(sequence_control_frame, width=20)
        self.sequence_entry.grid(row=0, column=0, padx=(0, 5))

        ttk.Button(sequence_control_frame, text="添加", command=self.add_to_sequence).grid(row=0, column=1, padx=2)
        ttk.Button(sequence_control_frame, text="清空", command=self.clear_sequence).grid(row=0, column=2, padx=2)

        # 序列显示
        self.sequence_listbox = tk.Listbox(self.sequence_frame, height=4, width=30)
        self.sequence_listbox.grid(row=2, column=0, columnspan=2, pady=5)

        sequence_btn_frame = ttk.Frame(self.sequence_frame)
        sequence_btn_frame.grid(row=3, column=0, columnspan=2, pady=2)

        ttk.Button(sequence_btn_frame, text="删除选中", command=self.remove_from_sequence).grid(row=0, column=0, padx=2)

        self.repeat_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(sequence_btn_frame, text="循环播放", variable=self.repeat_var).grid(row=0, column=1, padx=(10, 0))

        # 鼠标设置框架
        self.mouse_frame = ttk.Frame(settings_frame)
        self.mouse_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 鼠标按钮选择
        mouse_btn_frame = ttk.Frame(self.mouse_frame)
        mouse_btn_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)

        ttk.Label(mouse_btn_frame, text="鼠标按钮:").grid(row=0, column=0, sticky=tk.W)
        self.mouse_button_var = tk.StringVar(value=self.config["mouse_button"])
        mouse_btn_combo = ttk.Combobox(mouse_btn_frame, textvariable=self.mouse_button_var, width=10)
        mouse_btn_combo['values'] = ('left', 'right', 'middle')
        mouse_btn_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # 点击次数
        ttk.Label(mouse_btn_frame, text="点击次数:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.click_count_var = tk.StringVar(value=str(self.config["click_count"]))
        click_count_spin = ttk.Spinbox(mouse_btn_frame, textvariable=self.click_count_var, from_=1, to=10, width=5)
        click_count_spin.grid(row=0, column=3, sticky=tk.W, padx=(10, 0))

        # 坐标设置
        coord_frame = ttk.Frame(self.mouse_frame)
        coord_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(coord_frame, text="X坐标:").grid(row=0, column=0, sticky=tk.W)
        self.mouse_x_var = tk.StringVar(value=str(self.config["mouse_x"]))
        ttk.Entry(coord_frame, textvariable=self.mouse_x_var, width=8).grid(row=0, column=1, sticky=tk.W, padx=(5, 0))

        ttk.Label(coord_frame, text="Y坐标:").grid(row=0, column=2, sticky=tk.W, padx=(20, 0))
        self.mouse_y_var = tk.StringVar(value=str(self.config["mouse_y"]))
        ttk.Entry(coord_frame, textvariable=self.mouse_y_var, width=8).grid(row=0, column=3, sticky=tk.W, padx=(5, 0))

        ttk.Button(coord_frame, text="获取当前位置", command=self.get_current_mouse_pos).grid(row=0, column=4, padx=(10, 0))

        # 随机偏移
        offset_frame = ttk.Frame(self.mouse_frame)
        offset_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(offset_frame, text="随机偏移(像素):").grid(row=0, column=0, sticky=tk.W)
        self.mouse_offset_var = tk.StringVar(value=str(self.config["mouse_random_offset"]))
        ttk.Entry(offset_frame, textvariable=self.mouse_offset_var, width=8).grid(row=0, column=1, sticky=tk.W, padx=(5, 0))

        ttk.Label(offset_frame, text="(0=精确点击, >0=随机偏移)", font=("Arial", 8)).grid(row=0, column=2, sticky=tk.W, padx=(10, 0))
        
        # 时间设置框架
        timing_frame = ttk.Frame(settings_frame)
        timing_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)

        ttk.Label(timing_frame, text="最小间隔(秒):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.min_interval_var = tk.StringVar(value=str(self.config["min_interval"]))
        ttk.Entry(timing_frame, textvariable=self.min_interval_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(timing_frame, text="最大间隔(秒):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.max_interval_var = tk.StringVar(value=str(self.config["max_interval"]))
        ttk.Entry(timing_frame, textvariable=self.max_interval_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(timing_frame, text="按键持续时间(秒):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.press_duration_var = tk.StringVar(value=str(self.config["press_duration"]))
        ttk.Entry(timing_frame, textvariable=self.press_duration_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 高级定时设置框架
        advanced_frame = ttk.LabelFrame(main_frame, text="高级定时设置", padding="10")
        advanced_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        # 延迟启动
        ttk.Label(advanced_frame, text="延迟启动(秒):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.delay_start_var = tk.StringVar(value=str(self.config["delay_start"]))
        ttk.Entry(advanced_frame, textvariable=self.delay_start_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Label(advanced_frame, text="(0=立即开始)", font=("Arial", 8)).grid(row=0, column=2, sticky=tk.W, padx=(5, 0))

        # 最大运行时长
        ttk.Label(advanced_frame, text="最大运行时长(秒):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.max_duration_var = tk.StringVar(value=str(self.config["max_duration"]))
        ttk.Entry(advanced_frame, textvariable=self.max_duration_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Label(advanced_frame, text="(0=无限制)", font=("Arial", 8)).grid(row=1, column=2, sticky=tk.W, padx=(5, 0))

        # 最大循环次数
        ttk.Label(advanced_frame, text="最大循环次数:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.max_cycles_var = tk.StringVar(value=str(self.config["max_cycles"]))
        ttk.Entry(advanced_frame, textvariable=self.max_cycles_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        ttk.Label(advanced_frame, text="(0=无限制)", font=("Arial", 8)).grid(row=2, column=2, sticky=tk.W, padx=(5, 0))

        # 运行状态显示
        status_info_frame = ttk.Frame(advanced_frame)
        status_info_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.runtime_label = ttk.Label(status_info_frame, text="运行时间: 0秒")
        self.runtime_label.grid(row=0, column=0, sticky=tk.W)

        self.cycles_label = ttk.Label(status_info_frame, text="已完成循环: 0次")
        self.cycles_label.grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        self.error_label = ttk.Label(status_info_frame, text="错误次数: 0", foreground="red")
        self.error_label.grid(row=1, column=0, sticky=tk.W)

        # 初始化模式
        self.on_mode_change()
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=6, column=0, columnspan=2, pady=10)
        
        self.start_button = ttk.Button(control_frame, text="开始", command=self.toggle_simulation)
        self.start_button.grid(row=0, column=0, padx=5)
        
        ttk.Button(control_frame, text="保存配置", command=self.save_config).grid(row=0, column=1, padx=5)
        ttk.Button(control_frame, text="加载配置", command=self.load_config).grid(row=0, column=2, padx=5)
        ttk.Button(control_frame, text="重置设置", command=self.reset_config).grid(row=0, column=3, padx=5)

        # 快捷操作按钮
        quick_frame = ttk.Frame(main_frame)
        quick_frame.grid(row=8, column=0, columnspan=2, pady=5)

        ttk.Label(quick_frame, text="快捷设置:", font=("Arial", 9, "bold")).grid(row=0, column=0, sticky=tk.W)

        quick_buttons_frame = ttk.Frame(quick_frame)
        quick_buttons_frame.grid(row=1, column=0, columnspan=2, pady=5)

        ttk.Button(quick_buttons_frame, text="游戏挂机", command=lambda: self.apply_preset("gaming")).grid(row=0, column=0, padx=2)
        ttk.Button(quick_buttons_frame, text="办公自动化", command=lambda: self.apply_preset("office")).grid(row=0, column=1, padx=2)
        ttk.Button(quick_buttons_frame, text="测试模式", command=lambda: self.apply_preset("test")).grid(row=0, column=2, padx=2)
        ttk.Button(quick_buttons_frame, text="查看日志", command=self.show_logs).grid(row=0, column=3, padx=2)
        
        # 信息显示
        info_frame = ttk.LabelFrame(main_frame, text="使用说明", padding="10")
        info_frame.grid(row=9, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        info_text = """1. 设置目标按键和间隔时间
2. 点击"开始"或按热键 ~ 开始/停止模拟
3. 程序会模拟人类自然按键习惯
4. 间隔时间随机变化，更加自然"""
        
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).grid(row=0, column=0, sticky=tk.W)
    
    def start_hotkey_listener(self):
        """启动全局热键监听"""
        def on_press(key):
            try:
                if hasattr(key, 'char') and key.char == '`':  # ~ 键
                    self.toggle_simulation()
            except AttributeError:
                pass
        
        self.hotkey_listener = Listener(on_press=on_press)
        self.hotkey_listener.start()

    def on_mode_change(self):
        """模式切换时的处理"""
        mode = self.mode_var.get()
        if mode == "single":
            self.single_frame.grid()
            self.sequence_frame.grid_remove()
            self.mouse_frame.grid_remove()
        elif mode == "sequence":
            self.single_frame.grid_remove()
            self.sequence_frame.grid()
            self.mouse_frame.grid_remove()
        else:  # mouse mode
            self.single_frame.grid_remove()
            self.sequence_frame.grid_remove()
            self.mouse_frame.grid()

    def get_current_mouse_pos(self):
        """获取当前鼠标位置"""
        try:
            from pynput.mouse import Controller as MouseController
            mouse_controller = MouseController()
            x, y = mouse_controller.position
            self.mouse_x_var.set(str(int(x)))
            self.mouse_y_var.set(str(int(y)))
            messagebox.showinfo("成功", f"已获取当前鼠标位置: ({int(x)}, {int(y)})")
            logging.info(f"获取鼠标位置: ({int(x)}, {int(y)})")
        except Exception as e:
            self.handle_error("获取鼠标位置失败", e)
            messagebox.showerror("错误", f"获取鼠标位置失败: {e}")

    def add_to_sequence(self):
        """添加按键到序列"""
        key = self.sequence_entry.get().strip()
        if key:
            self.sequence_listbox.insert(tk.END, key)
            self.sequence_entry.delete(0, tk.END)

    def remove_from_sequence(self):
        """从序列中删除选中的按键"""
        selection = self.sequence_listbox.curselection()
        if selection:
            self.sequence_listbox.delete(selection[0])

    def clear_sequence(self):
        """清空按键序列"""
        self.sequence_listbox.delete(0, tk.END)

    def get_sequence_list(self):
        """获取当前序列列表"""
        return [self.sequence_listbox.get(i) for i in range(self.sequence_listbox.size())]
    
    def toggle_simulation(self):
        """切换模拟状态"""
        if self.is_running:
            self.stop_simulation()
        else:
            self.start_simulation()
    
    def start_simulation(self):
        """开始按键模拟"""
        try:
            # 更新配置
            mode = self.mode_var.get()
            self.config["target_key"] = self.key_var.get()
            self.config["min_interval"] = float(self.min_interval_var.get())
            self.config["max_interval"] = float(self.max_interval_var.get())
            self.config["press_duration"] = float(self.press_duration_var.get())
            self.config["sequence_mode"] = mode == "sequence"
            self.config["mouse_mode"] = mode == "mouse"
            self.config["key_sequence"] = self.get_sequence_list()
            self.config["sequence_repeat"] = self.repeat_var.get()

            # 鼠标模式配置
            if self.config["mouse_mode"]:
                self.config["mouse_button"] = self.mouse_button_var.get()
                self.config["mouse_x"] = int(self.mouse_x_var.get())
                self.config["mouse_y"] = int(self.mouse_y_var.get())
                self.config["mouse_random_offset"] = int(self.mouse_offset_var.get())
                self.config["click_count"] = int(self.click_count_var.get())

            # 高级定时配置
            self.config["delay_start"] = float(self.delay_start_var.get())
            self.config["max_duration"] = float(self.max_duration_var.get())
            self.config["max_cycles"] = int(self.max_cycles_var.get())

            if self.config["min_interval"] >= self.config["max_interval"]:
                messagebox.showerror("错误", "最小间隔必须小于最大间隔")
                return

            if self.config["sequence_mode"] and not self.config["key_sequence"]:
                messagebox.showerror("错误", "序列模式下必须添加至少一个按键")
                return

            self.is_running = True
            self.cycle_count = 0
            self.error_count = 0
            self.start_time = time.time()
            self.status_label.config(text="运行中", foreground="green")
            self.start_button.config(text="停止")

            # 记录启动日志
            mode_name = "鼠标模式" if self.config["mouse_mode"] else ("序列模式" if self.config["sequence_mode"] else "单键模式")
            logging.info(f"开始模拟 - 模式: {mode_name}")

            # 启动模拟线程
            self.simulation_thread = threading.Thread(target=self.simulation_worker, daemon=True)
            self.simulation_thread.start()

            # 启动状态更新线程
            self.status_thread = threading.Thread(target=self.update_status_worker, daemon=True)
            self.status_thread.start()

        except ValueError as e:
            self.handle_error("配置参数错误", e)
            messagebox.showerror("错误", "请输入有效的数字")
        except Exception as e:
            self.handle_error("启动模拟失败", e)
            messagebox.showerror("错误", f"启动失败: {e}")
    
    def stop_simulation(self):
        """停止按键模拟"""
        self.is_running = False
        self.status_label.config(text="已停止", foreground="red")
        self.start_button.config(text="开始")

        # 记录停止日志
        if self.start_time:
            elapsed = time.time() - self.start_time
            logging.info(f"停止模拟 - 运行时长: {elapsed:.1f}秒, 完成循环: {self.cycle_count}次, 错误次数: {self.error_count}")
    
    def simulation_worker(self):
        """按键/鼠标模拟工作线程"""
        from pynput.keyboard import Controller as KeyboardController
        from pynput.mouse import Controller as MouseController

        # 延迟启动
        if self.config["delay_start"] > 0:
            delay_time = self.config["delay_start"]
            self.status_label.config(text=f"延迟启动中... {delay_time:.0f}秒", foreground="orange")
            time.sleep(delay_time)
            if not self.is_running:  # 检查是否在延迟期间被停止
                return
            self.status_label.config(text="运行中", foreground="green")

        keyboard_controller = KeyboardController()
        mouse_controller = MouseController()
        sequence_index = 0

        while self.is_running:
            try:
                # 检查运行时长限制
                if self.config["max_duration"] > 0:
                    elapsed = time.time() - self.start_time
                    if elapsed >= self.config["max_duration"]:
                        print(f"达到最大运行时长 {self.config['max_duration']} 秒，停止模拟")
                        break

                # 检查循环次数限制
                if self.config["max_cycles"] > 0 and self.cycle_count >= self.config["max_cycles"]:
                    print(f"达到最大循环次数 {self.config['max_cycles']} 次，停止模拟")
                    break

                if self.config["mouse_mode"]:
                    # 鼠标模式
                    self.click_mouse(mouse_controller)
                    self.cycle_count += 1
                elif self.config["sequence_mode"]:
                    # 序列模式
                    if not self.config["key_sequence"]:
                        break

                    target_key = self.config["key_sequence"][sequence_index]
                    sequence_index += 1

                    # 检查是否需要重置序列
                    if sequence_index >= len(self.config["key_sequence"]):
                        self.cycle_count += 1  # 完成一轮序列
                        if self.config["sequence_repeat"]:
                            sequence_index = 0
                        else:
                            break  # 序列完成，停止模拟

                    # 执行按键
                    self.press_key(keyboard_controller, target_key)
                else:
                    # 单键模式
                    target_key = self.config["target_key"]
                    self.press_key(keyboard_controller, target_key)
                    self.cycle_count += 1

                # 随机间隔时间
                interval = random.uniform(self.config["min_interval"], self.config["max_interval"])
                time.sleep(interval)

            except Exception as e:
                self.handle_error("模拟操作执行失败", e)
                # 短暂延迟后继续，避免连续错误
                time.sleep(1)
                # 如果错误过多，退出循环
                if self.error_count >= 10:
                    break

        # 模拟结束，更新状态
        self.root.after(0, self.stop_simulation)

    def press_key(self, controller, key_name):
        """执行按键操作"""
        try:
            key_name = key_name.lower()

            # 检查是否为组合键
            if '+' in key_name:
                self.press_combination_key(controller, key_name)
                return

            # 特殊按键映射
            special_keys = {
                "space": Key.space,
                "enter": Key.enter,
                "tab": Key.tab,
                "esc": Key.esc,
                "escape": Key.esc,
                "backspace": Key.backspace,
                "delete": Key.delete,
                "shift": Key.shift,
                "ctrl": Key.ctrl,
                "alt": Key.alt,
                "win": Key.cmd,
                "cmd": Key.cmd,
                "up": Key.up,
                "down": Key.down,
                "left": Key.left,
                "right": Key.right,
                "home": Key.home,
                "end": Key.end,
                "page_up": Key.page_up,
                "page_down": Key.page_down,
                "insert": Key.insert,
                "caps_lock": Key.caps_lock,
                "num_lock": Key.num_lock,
                "scroll_lock": Key.scroll_lock,
                "print_screen": Key.print_screen,
                "pause": Key.pause,
                "menu": Key.menu
            }

            # 功能键
            for i in range(1, 13):
                special_keys[f"f{i}"] = getattr(Key, f"f{i}")

            if key_name in special_keys:
                key = special_keys[key_name]
                controller.press(key)
                time.sleep(self.config["press_duration"])
                controller.release(key)
            else:
                # 普通字符按键
                controller.press(key_name)
                time.sleep(self.config["press_duration"])
                controller.release(key_name)

        except Exception as e:
            print(f"按键 {key_name} 执行失败: {e}")

    def press_combination_key(self, controller, combination):
        """执行组合键操作"""
        try:
            parts = combination.split('+')
            modifier_keys = []
            main_key = parts[-1]  # 最后一个是主按键

            # 解析修饰键
            key_map = {
                "ctrl": Key.ctrl,
                "alt": Key.alt,
                "shift": Key.shift,
                "win": Key.cmd,
                "cmd": Key.cmd
            }

            # 按下修饰键
            for part in parts[:-1]:
                if part in key_map:
                    modifier_keys.append(key_map[part])
                    controller.press(key_map[part])

            time.sleep(0.01)  # 短暂延迟

            # 按下主按键
            if main_key in key_map:
                controller.press(key_map[main_key])
                time.sleep(self.config["press_duration"])
                controller.release(key_map[main_key])
            else:
                controller.press(main_key)
                time.sleep(self.config["press_duration"])
                controller.release(main_key)

            # 释放修饰键（逆序）
            for modifier in reversed(modifier_keys):
                controller.release(modifier)

        except Exception as e:
            print(f"组合键 {combination} 执行失败: {e}")

    def click_mouse(self, controller):
        """执行鼠标点击操作"""
        try:
            # 计算点击位置（加入随机偏移）
            base_x = self.config["mouse_x"]
            base_y = self.config["mouse_y"]
            offset = self.config["mouse_random_offset"]

            if offset > 0:
                # 添加随机偏移
                x = base_x + random.randint(-offset, offset)
                y = base_y + random.randint(-offset, offset)
            else:
                x, y = base_x, base_y

            # 移动到目标位置
            controller.position = (x, y)
            time.sleep(0.05)  # 短暂延迟，模拟真实移动

            # 确定按钮类型
            button_map = {
                "left": Button.left,
                "right": Button.right,
                "middle": Button.middle
            }
            button = button_map.get(self.config["mouse_button"], Button.left)

            # 执行点击
            click_count = self.config["click_count"]
            for i in range(click_count):
                controller.press(button)
                time.sleep(self.config["press_duration"])
                controller.release(button)
                if i < click_count - 1:  # 多次点击之间的间隔
                    time.sleep(0.05)

        except Exception as e:
            print(f"鼠标点击执行失败: {e}")

    def update_status_worker(self):
        """状态更新工作线程"""
        while self.is_running:
            try:
                if self.start_time:
                    elapsed = time.time() - self.start_time
                    # 使用after方法在主线程中更新UI
                    self.root.after(0, self.update_status_display, elapsed, self.cycle_count)
                time.sleep(1)  # 每秒更新一次
            except Exception as e:
                print(f"状态更新出错: {e}")
                break

    def update_status_display(self, elapsed_time, cycles):
        """更新状态显示（在主线程中调用）"""
        try:
            # 格式化运行时间
            hours = int(elapsed_time // 3600)
            minutes = int((elapsed_time % 3600) // 60)
            seconds = int(elapsed_time % 60)

            if hours > 0:
                time_str = f"{hours}:{minutes:02d}:{seconds:02d}"
            else:
                time_str = f"{minutes}:{seconds:02d}"

            self.runtime_label.config(text=f"运行时间: {time_str}")
            self.cycles_label.config(text=f"已完成循环: {cycles}次")
            self.error_label.config(text=f"错误次数: {self.error_count}")
        except Exception as e:
            print(f"更新状态显示出错: {e}")

    def handle_error(self, error_msg, exception=None):
        """统一错误处理"""
        self.error_count += 1
        self.last_error_time = time.time()

        # 记录错误日志
        if exception:
            logging.error(f"{error_msg}: {str(exception)}")
        else:
            logging.error(error_msg)

        # 如果错误过多，自动停止
        if self.error_count >= 10:
            logging.warning("错误次数过多，自动停止模拟")
            self.root.after(0, self.stop_simulation)
            self.root.after(0, lambda: messagebox.showwarning("警告", "检测到过多错误，已自动停止模拟。请检查设置或查看日志文件。"))

    def reset_config(self):
        """重置配置到默认值"""
        if messagebox.askyesno("确认", "确定要重置所有设置到默认值吗？"):
            # 重置到默认配置
            default_config = {
                "target_key": "space",
                "min_interval": 0.5,
                "max_interval": 2.0,
                "press_duration": 0.1,
                "hotkey": "`",
                "sequence_mode": False,
                "key_sequence": [],
                "sequence_repeat": True,
                "mouse_mode": False,
                "mouse_button": "left",
                "mouse_x": 100,
                "mouse_y": 100,
                "mouse_random_offset": 5,
                "click_count": 1,
                "max_duration": 0,
                "max_cycles": 0,
                "delay_start": 0
            }

            self.config.update(default_config)
            self.update_ui_from_config()
            logging.info("配置已重置到默认值")
            messagebox.showinfo("成功", "配置已重置到默认值")

    def apply_preset(self, preset_name):
        """应用预设配置"""
        presets = {
            "gaming": {
                "target_key": "space",
                "min_interval": 1.0,
                "max_interval": 3.0,
                "press_duration": 0.1,
                "max_duration": 3600,  # 1小时
                "delay_start": 5
            },
            "office": {
                "target_key": "ctrl+s",
                "min_interval": 30.0,
                "max_interval": 60.0,
                "press_duration": 0.1,
                "max_cycles": 100
            },
            "test": {
                "target_key": "a",
                "min_interval": 0.5,
                "max_interval": 1.0,
                "press_duration": 0.05,
                "max_cycles": 10
            }
        }

        if preset_name in presets:
            preset = presets[preset_name]
            for key, value in preset.items():
                if key in self.config:
                    self.config[key] = value

            self.update_ui_from_config()
            logging.info(f"应用预设配置: {preset_name}")
            messagebox.showinfo("成功", f"已应用{preset_name}预设配置")

    def update_ui_from_config(self):
        """从配置更新UI"""
        try:
            self.key_var.set(self.config["target_key"])
            self.min_interval_var.set(str(self.config["min_interval"]))
            self.max_interval_var.set(str(self.config["max_interval"]))
            self.press_duration_var.set(str(self.config["press_duration"]))

            if hasattr(self, 'delay_start_var'):
                self.delay_start_var.set(str(self.config.get("delay_start", 0)))
                self.max_duration_var.set(str(self.config.get("max_duration", 0)))
                self.max_cycles_var.set(str(self.config.get("max_cycles", 0)))

            if hasattr(self, 'mouse_button_var'):
                self.mouse_button_var.set(self.config.get("mouse_button", "left"))
                self.mouse_x_var.set(str(self.config.get("mouse_x", 100)))
                self.mouse_y_var.set(str(self.config.get("mouse_y", 100)))
                self.mouse_offset_var.set(str(self.config.get("mouse_random_offset", 5)))
                self.click_count_var.set(str(self.config.get("click_count", 1)))
        except Exception as e:
            self.handle_error("更新UI失败", e)

    def show_logs(self):
        """显示日志文件"""
        try:
            log_dir = "logs"
            if os.path.exists(log_dir):
                # 在Windows上打开文件夹
                if os.name == 'nt':
                    os.startfile(log_dir)
                else:
                    # 在Linux/Mac上打开文件夹
                    import subprocess
                    subprocess.run(['xdg-open', log_dir])
            else:
                messagebox.showinfo("提示", "日志文件夹不存在")
        except Exception as e:
            self.handle_error("打开日志文件夹失败", e)
            messagebox.showerror("错误", f"无法打开日志文件夹: {e}")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            mode = self.mode_var.get()
            self.config["target_key"] = self.key_var.get()
            self.config["min_interval"] = float(self.min_interval_var.get())
            self.config["max_interval"] = float(self.max_interval_var.get())
            self.config["press_duration"] = float(self.press_duration_var.get())
            self.config["sequence_mode"] = mode == "sequence"
            self.config["mouse_mode"] = mode == "mouse"
            self.config["key_sequence"] = self.get_sequence_list()
            self.config["sequence_repeat"] = self.repeat_var.get()

            # 保存鼠标设置
            if hasattr(self, 'mouse_button_var'):
                self.config["mouse_button"] = self.mouse_button_var.get()
                self.config["mouse_x"] = int(self.mouse_x_var.get())
                self.config["mouse_y"] = int(self.mouse_y_var.get())
                self.config["mouse_random_offset"] = int(self.mouse_offset_var.get())
                self.config["click_count"] = int(self.click_count_var.get())

            # 保存高级定时设置
            if hasattr(self, 'delay_start_var'):
                self.config["delay_start"] = float(self.delay_start_var.get())
                self.config["max_duration"] = float(self.max_duration_var.get())
                self.config["max_cycles"] = int(self.max_cycles_var.get())

            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists("config.json"):
                with open("config.json", "r", encoding="utf-8") as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)

                # 更新UI
                self.key_var.set(self.config["target_key"])
                self.min_interval_var.set(str(self.config["min_interval"]))
                self.max_interval_var.set(str(self.config["max_interval"]))
                self.press_duration_var.set(str(self.config["press_duration"]))

                # 更新模式
                if self.config.get("mouse_mode", False):
                    self.mode_var.set("mouse")
                elif self.config.get("sequence_mode", False):
                    self.mode_var.set("sequence")
                else:
                    self.mode_var.set("single")

                self.repeat_var.set(self.config.get("sequence_repeat", True))

                # 加载序列
                self.clear_sequence()
                for key in self.config.get("key_sequence", []):
                    self.sequence_listbox.insert(tk.END, key)

                # 加载鼠标设置
                if hasattr(self, 'mouse_button_var'):
                    self.mouse_button_var.set(self.config.get("mouse_button", "left"))
                    self.mouse_x_var.set(str(self.config.get("mouse_x", 100)))
                    self.mouse_y_var.set(str(self.config.get("mouse_y", 100)))
                    self.mouse_offset_var.set(str(self.config.get("mouse_random_offset", 5)))
                    self.click_count_var.set(str(self.config.get("click_count", 1)))

                # 加载高级定时设置
                if hasattr(self, 'delay_start_var'):
                    self.delay_start_var.set(str(self.config.get("delay_start", 0)))
                    self.max_duration_var.set(str(self.config.get("max_duration", 0)))
                    self.max_cycles_var.set(str(self.config.get("max_cycles", 0)))

                self.on_mode_change()
                messagebox.showinfo("成功", "配置已加载")
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
    
    def on_closing(self):
        """窗口关闭时的清理工作"""
        self.is_running = False
        if self.hotkey_listener:
            self.hotkey_listener.stop()
        self.root.destroy()

def main():
    root = tk.Tk()
    app = KeySimulatorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
