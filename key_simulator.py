import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import random
from pynput import keyboard
from pynput.keyboard import Key, Listener
import json
import os

class KeySimulatorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("按键模拟器 - Key Simulator")
        self.root.geometry("500x400")
        self.root.resizable(False, False)
        
        # 应用状态
        self.is_running = False
        self.simulation_thread = None
        self.hotkey_listener = None
        
        # 配置参数
        self.config = {
            "target_key": "space",
            "min_interval": 0.5,
            "max_interval": 2.0,
            "press_duration": 0.1,
            "hotkey": "`",  # ~ 键
            "sequence_mode": False,
            "key_sequence": [],
            "sequence_repeat": True
        }
        
        self.load_config()
        self.setup_ui()
        self.start_hotkey_listener()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="按键模拟器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="状态", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="已停止", foreground="red")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.hotkey_label = ttk.Label(status_frame, text=f"热键: {self.config['hotkey']} (开关)")
        self.hotkey_label.grid(row=1, column=0, sticky=tk.W)
        
        # 设置框架
        settings_frame = ttk.LabelFrame(main_frame, text="设置", padding="10")
        settings_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 模式选择
        mode_frame = ttk.Frame(settings_frame)
        mode_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.mode_var = tk.StringVar(value="single")
        ttk.Radiobutton(mode_frame, text="单键模式", variable=self.mode_var, value="single", command=self.on_mode_change).grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="序列模式", variable=self.mode_var, value="sequence", command=self.on_mode_change).grid(row=0, column=1, sticky=tk.W, padx=(20, 0))

        # 单键设置框架
        self.single_frame = ttk.Frame(settings_frame)
        self.single_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(self.single_frame, text="目标按键:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.key_var = tk.StringVar(value=self.config["target_key"])
        key_combo = ttk.Combobox(self.single_frame, textvariable=self.key_var, width=15)
        key_combo['values'] = ('space', 'enter', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z')
        key_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 序列设置框架
        self.sequence_frame = ttk.Frame(settings_frame)
        self.sequence_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(self.sequence_frame, text="按键序列:").grid(row=0, column=0, sticky=tk.W, pady=2)

        sequence_control_frame = ttk.Frame(self.sequence_frame)
        sequence_control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)

        self.sequence_entry = ttk.Entry(sequence_control_frame, width=20)
        self.sequence_entry.grid(row=0, column=0, padx=(0, 5))

        ttk.Button(sequence_control_frame, text="添加", command=self.add_to_sequence).grid(row=0, column=1, padx=2)
        ttk.Button(sequence_control_frame, text="清空", command=self.clear_sequence).grid(row=0, column=2, padx=2)

        # 序列显示
        self.sequence_listbox = tk.Listbox(self.sequence_frame, height=4, width=30)
        self.sequence_listbox.grid(row=2, column=0, columnspan=2, pady=5)

        sequence_btn_frame = ttk.Frame(self.sequence_frame)
        sequence_btn_frame.grid(row=3, column=0, columnspan=2, pady=2)

        ttk.Button(sequence_btn_frame, text="删除选中", command=self.remove_from_sequence).grid(row=0, column=0, padx=2)

        self.repeat_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(sequence_btn_frame, text="循环播放", variable=self.repeat_var).grid(row=0, column=1, padx=(10, 0))
        
        # 时间设置框架
        timing_frame = ttk.Frame(settings_frame)
        timing_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)

        ttk.Label(timing_frame, text="最小间隔(秒):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.min_interval_var = tk.StringVar(value=str(self.config["min_interval"]))
        ttk.Entry(timing_frame, textvariable=self.min_interval_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(timing_frame, text="最大间隔(秒):").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.max_interval_var = tk.StringVar(value=str(self.config["max_interval"]))
        ttk.Entry(timing_frame, textvariable=self.max_interval_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        ttk.Label(timing_frame, text="按键持续时间(秒):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.press_duration_var = tk.StringVar(value=str(self.config["press_duration"]))
        ttk.Entry(timing_frame, textvariable=self.press_duration_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 初始化模式
        self.on_mode_change()
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        self.start_button = ttk.Button(control_frame, text="开始", command=self.toggle_simulation)
        self.start_button.grid(row=0, column=0, padx=5)
        
        ttk.Button(control_frame, text="保存配置", command=self.save_config).grid(row=0, column=1, padx=5)
        ttk.Button(control_frame, text="加载配置", command=self.load_config).grid(row=0, column=2, padx=5)
        
        # 信息显示
        info_frame = ttk.LabelFrame(main_frame, text="使用说明", padding="10")
        info_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        info_text = """1. 设置目标按键和间隔时间
2. 点击"开始"或按热键 ~ 开始/停止模拟
3. 程序会模拟人类自然按键习惯
4. 间隔时间随机变化，更加自然"""
        
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).grid(row=0, column=0, sticky=tk.W)
    
    def start_hotkey_listener(self):
        """启动全局热键监听"""
        def on_press(key):
            try:
                if hasattr(key, 'char') and key.char == '`':  # ~ 键
                    self.toggle_simulation()
            except AttributeError:
                pass
        
        self.hotkey_listener = Listener(on_press=on_press)
        self.hotkey_listener.start()

    def on_mode_change(self):
        """模式切换时的处理"""
        if self.mode_var.get() == "single":
            self.single_frame.grid()
            self.sequence_frame.grid_remove()
        else:
            self.single_frame.grid_remove()
            self.sequence_frame.grid()

    def add_to_sequence(self):
        """添加按键到序列"""
        key = self.sequence_entry.get().strip()
        if key:
            self.sequence_listbox.insert(tk.END, key)
            self.sequence_entry.delete(0, tk.END)

    def remove_from_sequence(self):
        """从序列中删除选中的按键"""
        selection = self.sequence_listbox.curselection()
        if selection:
            self.sequence_listbox.delete(selection[0])

    def clear_sequence(self):
        """清空按键序列"""
        self.sequence_listbox.delete(0, tk.END)

    def get_sequence_list(self):
        """获取当前序列列表"""
        return [self.sequence_listbox.get(i) for i in range(self.sequence_listbox.size())]
    
    def toggle_simulation(self):
        """切换模拟状态"""
        if self.is_running:
            self.stop_simulation()
        else:
            self.start_simulation()
    
    def start_simulation(self):
        """开始按键模拟"""
        try:
            # 更新配置
            self.config["target_key"] = self.key_var.get()
            self.config["min_interval"] = float(self.min_interval_var.get())
            self.config["max_interval"] = float(self.max_interval_var.get())
            self.config["press_duration"] = float(self.press_duration_var.get())
            self.config["sequence_mode"] = self.mode_var.get() == "sequence"
            self.config["key_sequence"] = self.get_sequence_list()
            self.config["sequence_repeat"] = self.repeat_var.get()

            if self.config["min_interval"] >= self.config["max_interval"]:
                messagebox.showerror("错误", "最小间隔必须小于最大间隔")
                return

            if self.config["sequence_mode"] and not self.config["key_sequence"]:
                messagebox.showerror("错误", "序列模式下必须添加至少一个按键")
                return

            self.is_running = True
            self.status_label.config(text="运行中", foreground="green")
            self.start_button.config(text="停止")

            # 启动模拟线程
            self.simulation_thread = threading.Thread(target=self.simulation_worker, daemon=True)
            self.simulation_thread.start()

        except ValueError:
            messagebox.showerror("错误", "请输入有效的数字")
    
    def stop_simulation(self):
        """停止按键模拟"""
        self.is_running = False
        self.status_label.config(text="已停止", foreground="red")
        self.start_button.config(text="开始")
    
    def simulation_worker(self):
        """按键模拟工作线程"""
        from pynput.keyboard import Controller

        keyboard_controller = Controller()
        sequence_index = 0

        while self.is_running:
            try:
                if self.config["sequence_mode"]:
                    # 序列模式
                    if not self.config["key_sequence"]:
                        break

                    target_key = self.config["key_sequence"][sequence_index]
                    sequence_index += 1

                    # 检查是否需要重置序列
                    if sequence_index >= len(self.config["key_sequence"]):
                        if self.config["sequence_repeat"]:
                            sequence_index = 0
                        else:
                            break  # 序列完成，停止模拟
                else:
                    # 单键模式
                    target_key = self.config["target_key"]

                # 执行按键
                self.press_key(keyboard_controller, target_key)

                # 随机间隔时间
                interval = random.uniform(self.config["min_interval"], self.config["max_interval"])
                time.sleep(interval)

            except Exception as e:
                print(f"模拟按键时出错: {e}")
                break

    def press_key(self, controller, key_name):
        """执行按键操作"""
        try:
            # 特殊按键处理
            if key_name.lower() == "space":
                controller.press(Key.space)
                time.sleep(self.config["press_duration"])
                controller.release(Key.space)
            elif key_name.lower() == "enter":
                controller.press(Key.enter)
                time.sleep(self.config["press_duration"])
                controller.release(Key.enter)
            elif key_name.lower() == "tab":
                controller.press(Key.tab)
                time.sleep(self.config["press_duration"])
                controller.release(Key.tab)
            elif key_name.lower() == "shift":
                controller.press(Key.shift)
                time.sleep(self.config["press_duration"])
                controller.release(Key.shift)
            elif key_name.lower() == "ctrl":
                controller.press(Key.ctrl)
                time.sleep(self.config["press_duration"])
                controller.release(Key.ctrl)
            elif key_name.lower() == "alt":
                controller.press(Key.alt)
                time.sleep(self.config["press_duration"])
                controller.release(Key.alt)
            else:
                # 普通字符按键
                controller.press(key_name.lower())
                time.sleep(self.config["press_duration"])
                controller.release(key_name.lower())
        except Exception as e:
            print(f"按键 {key_name} 执行失败: {e}")
    
    def save_config(self):
        """保存配置到文件"""
        try:
            self.config["target_key"] = self.key_var.get()
            self.config["min_interval"] = float(self.min_interval_var.get())
            self.config["max_interval"] = float(self.max_interval_var.get())
            self.config["press_duration"] = float(self.press_duration_var.get())
            self.config["sequence_mode"] = self.mode_var.get() == "sequence"
            self.config["key_sequence"] = self.get_sequence_list()
            self.config["sequence_repeat"] = self.repeat_var.get()

            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists("config.json"):
                with open("config.json", "r", encoding="utf-8") as f:
                    loaded_config = json.load(f)
                    self.config.update(loaded_config)

                # 更新UI
                self.key_var.set(self.config["target_key"])
                self.min_interval_var.set(str(self.config["min_interval"]))
                self.max_interval_var.set(str(self.config["max_interval"]))
                self.press_duration_var.set(str(self.config["press_duration"]))

                # 更新模式和序列
                if self.config.get("sequence_mode", False):
                    self.mode_var.set("sequence")
                else:
                    self.mode_var.set("single")

                self.repeat_var.set(self.config.get("sequence_repeat", True))

                # 加载序列
                self.clear_sequence()
                for key in self.config.get("key_sequence", []):
                    self.sequence_listbox.insert(tk.END, key)

                self.on_mode_change()
                messagebox.showinfo("成功", "配置已加载")
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {e}")
    
    def on_closing(self):
        """窗口关闭时的清理工作"""
        self.is_running = False
        if self.hotkey_listener:
            self.hotkey_listener.stop()
        self.root.destroy()

def main():
    root = tk.Tk()
    app = KeySimulatorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
