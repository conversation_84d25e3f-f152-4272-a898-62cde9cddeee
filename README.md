# 按键模拟器 (Key Simulator)

一个功能强大的按键模拟应用程序，支持单键模式和序列模式，具有人性化的随机间隔功能。

## 功能特性

### 🎯 核心功能
- **单键模式**: 循环模拟单个按键
- **序列模式**: 按顺序模拟多个按键
- **全局热键**: 使用 ` 键（波浪号键）快速开关
- **人性化模拟**: 随机间隔时间，模拟真实人类操作
- **多线程**: 后台运行，不影响UI响应

### ⚙️ 设置选项
- **按键选择**: 支持字母、空格、回车等常用按键
- **时间控制**: 可设置最小/最大间隔时间和按键持续时间
- **序列管理**: 添加、删除、清空按键序列
- **循环播放**: 序列模式支持循环或单次播放
- **配置保存**: 自动保存和加载配置

## 使用方法

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动应用
```bash
python key_simulator.py
```

### 基本操作

#### 单键模式
1. 选择"单键模式"
2. 在下拉菜单中选择目标按键
3. 设置间隔时间范围
4. 点击"开始"或按 ` 键开始模拟

#### 序列模式
1. 选择"序列模式"
2. 在输入框中输入按键名称，点击"添加"
3. 重复添加多个按键构建序列
4. 选择是否循环播放
5. 点击"开始"或按 ` 键开始模拟

### 支持的按键
- **字母**: a-z
- **特殊键**: space, enter, tab, shift, ctrl, alt
- **自定义**: 可在代码中扩展更多按键

### 配置管理
- **保存配置**: 点击"保存配置"将当前设置保存到 config.json
- **加载配置**: 点击"加载配置"从文件恢复设置
- **自动加载**: 启动时自动加载上次保存的配置

## 技术特性

### 🔧 技术实现
- **GUI框架**: Tkinter (Python内置)
- **按键控制**: pynput库
- **多线程**: threading模块
- **配置存储**: JSON格式

### 🛡️ 安全特性
- **全局热键**: 随时可以通过 ` 键停止模拟
- **异常处理**: 完善的错误处理机制
- **资源清理**: 程序退出时自动清理资源

### ⚡ 性能优化
- **后台运行**: 按键模拟在独立线程中运行
- **低CPU占用**: 高效的事件循环设计
- **内存友好**: 最小化内存使用

## 使用场景

- **游戏辅助**: 自动执行重复操作
- **测试工具**: 模拟用户输入进行软件测试
- **办公自动化**: 自动化重复的键盘操作
- **演示工具**: 自动演示按键序列

## 注意事项

1. **管理员权限**: 某些应用可能需要管理员权限才能接收模拟按键
2. **防病毒软件**: 部分防病毒软件可能会拦截按键模拟功能
3. **合理使用**: 请遵守相关软件的使用条款，不要用于作弊
4. **热键冲突**: 确保 ` 键没有被其他软件占用

## 扩展功能

### 未来计划
- [ ] 支持鼠标点击模拟
- [ ] 添加更多特殊按键支持
- [ ] 支持组合键（如Ctrl+C）
- [ ] 添加录制功能
- [ ] 支持条件触发
- [ ] 添加脚本模式

### 自定义扩展
可以通过修改 `press_key` 方法来添加更多按键支持，或者在 `simulation_worker` 方法中添加更复杂的逻辑。

## 许可证

本项目仅供学习和合法用途使用。使用者需要遵守当地法律法规和相关软件的使用条款。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
