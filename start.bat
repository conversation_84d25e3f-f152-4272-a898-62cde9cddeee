@echo off
echo ========================================
echo        按键模拟器 - Key Simulator
echo ========================================
echo.
echo 正在启动按键模拟器...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
pip show pynput >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 启动应用
echo 启动应用程序...
python key_simulator.py

if errorlevel 1 (
    echo.
    echo 应用程序异常退出
    pause
)
